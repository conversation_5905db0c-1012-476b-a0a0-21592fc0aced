/**
 * Test script for PayU response handling
 * Run this to test if your PayU response handler is working correctly
 */

const axios = require('axios');

// Configuration
const SERVER_URL = 'http://localhost:3000'; // Update with your server URL
const API_ENDPOINT = '/api/v1/user/payment/response-payu';

// Test scenarios
const testScenarios = [
    {
        name: 'Successful Payment',
        payload: {
            status: 'success',
            txnid: 'test_txn_' + Date.now(),
            hash: 'test_hash_123',
            response: {
                status: 'success',
                txnid: 'test_txn_' + Date.now(),
                amount: '100.00',
                productinfo: 'Test Payment',
                firstname: 'Test User',
                email: '<EMAIL>'
            }
        }
    },
    {
        name: 'Failed Payment',
        payload: {
            status: 'failure',
            txnid: 'test_txn_fail_' + Date.now(),
            hash: 'test_hash_456',
            response: {
                status: 'failure',
                txnid: 'test_txn_fail_' + Date.now(),
                error: 'Payment declined by bank'
            }
        }
    },
    {
        name: 'Cancelled Payment',
        payload: {
            status: 'cancel',
            txnid: 'test_txn_cancel_' + Date.now(),
            hash: '',
            response: {
                status: 'cancel',
                txnid: 'test_txn_cancel_' + Date.now(),
                isTxnInitiated: false
            }
        }
    }
];

async function testPayUResponse() {
    console.log('🧪 Starting PayU Response Handler Tests...\n');

    for (const scenario of testScenarios) {
        console.log(`\n📋 Testing: ${scenario.name}`);
        console.log(`📤 Payload:`, JSON.stringify(scenario.payload, null, 2));

        try {
            const response = await axios.post(
                `${SERVER_URL}${API_ENDPOINT}`,
                scenario.payload,
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000
                }
            );

            console.log(`✅ Response Status: ${response.status}`);
            console.log(`📥 Response Data:`, response.data);

        } catch (error) {
            if (error.response) {
                console.log(`❌ Error Status: ${error.response.status}`);
                console.log(`📥 Error Data:`, error.response.data);
            } else if (error.request) {
                console.log(`❌ Network Error: No response received`);
                console.log(`📡 Request:`, error.request);
            } else {
                console.log(`❌ Error:`, error.message);
            }
        }

        console.log('─'.repeat(50));
    }

    console.log('\n🏁 PayU Response Handler Tests Completed');
}

// Health check function
async function checkServerHealth() {
    console.log('🏥 Checking server health...');
    
    try {
        const response = await axios.get(`${SERVER_URL}/api/v1/payu/health`, {
            timeout: 5000
        });
        
        console.log('✅ Server is healthy:', response.data);
        return true;
    } catch (error) {
        console.log('❌ Server health check failed:', error.message);
        return false;
    }
}

// Main execution
async function main() {
    console.log('🚀 PayU Response Handler Test Suite');
    console.log('=====================================\n');

    // Check server health first
    const isHealthy = await checkServerHealth();
    
    if (!isHealthy) {
        console.log('\n⚠️ Server appears to be down. Please start your server first.');
        console.log('💡 Make sure your server is running on:', SERVER_URL);
        return;
    }

    // Run tests
    await testPayUResponse();
}

// Run the tests
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = { testPayUResponse, checkServerHealth };
