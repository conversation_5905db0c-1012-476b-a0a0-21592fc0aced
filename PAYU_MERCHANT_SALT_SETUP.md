# PayU Merchant Salt Setup Guide

## 🚨 CRITICAL: Hash Generation Fix Required

The PayU integration is failing with "Invalid Hash" error because we need your **PayU Merchant Salt** to generate proper hashes.

## Current Status
✅ **Hash Generation Working**: Client-side hash generation is implemented correctly  
✅ **PayU SDK Integration**: All callbacks working properly  
❌ **Missing Merchant Salt**: Hashes are being rejected by PayU (Error 5019)  

## How to Get Your PayU Merchant Salt

### Step 1: Access PayU Dashboard
1. Log in to your PayU Merchant Dashboard
2. Go to **Settings** → **API Keys** or **Integration**
3. You should see:
   - **Merchant Key**: `AbM7T2` (already configured)
   - **Merchant Salt**: `YOUR_SALT_HERE` (this is what we need)

### Step 2: Copy Your Merchant Salt
- The salt is usually a long string (32+ characters)
- It looks something like: `eCwWELxi`
- **NEVER** share this salt publicly or commit it to version control

### Step 3: Configure the Salt in Your App

#### Option A: Environment Configuration (Recommended)
Create a file `lib/config/payu_config.dart`:

```dart
class PayUConfig {
  // Get this from your PayU dashboard
  static const String merchantKey = 'AbM7T2';
  static const String merchantSalt = 'YOUR_ACTUAL_SALT_HERE'; // Replace with real salt
  
  // Environment settings
  static const bool isProduction = false; // Set to true for production
}
```

#### Option B: Direct Configuration (Quick Fix)
Update the salt in `lib/services/payment/payu_service.dart` line 407:

```dart
const String merchantSalt = 'YOUR_ACTUAL_SALT_HERE'; // Replace with your real salt
```

## Security Best Practices

### ⚠️ Important Security Notes:
1. **Never commit salt to Git**: Add salt to `.gitignore` or use environment variables
2. **Use different salts**: Test and production should have different salts
3. **Rotate regularly**: Change salt periodically for security

### Recommended Secure Setup:
```dart
class PayUConfig {
  static String get merchantSalt {
    // In production, get from secure storage or environment
    if (kDebugMode) {
      return 'YOUR_TEST_SALT'; // Test environment salt
    } else {
      return 'YOUR_PRODUCTION_SALT'; // Production environment salt
    }
  }
}
```

## Testing After Salt Configuration

### Step 1: Update the Salt
Replace `YOUR_MERCHANT_SALT_HERE` with your actual salt from PayU dashboard

### Step 2: Test Payment Flow
1. Run the app: `flutter run`
2. Try a PayU payment
3. Check logs for hash generation success

### Step 3: Verify Success
Look for these log messages:
```
🔐 PAYU: ✅ Hash generated successfully with merchant salt
🔐 PAYU: Hash length: 128
```

If you see "Invalid Hash" error, double-check your salt.

## Common Issues

### Issue 1: Still Getting "Invalid Hash"
**Solution**: Verify the salt is exactly as shown in PayU dashboard (no extra spaces)

### Issue 2: Hash Generation Fails
**Solution**: Check that salt is a valid string (not null or empty)

### Issue 3: Different Errors in Production
**Solution**: Ensure production salt is different from test salt

## Next Steps After Salt Configuration

1. **Test thoroughly**: Try multiple payment scenarios
2. **Monitor logs**: Watch for any hash-related errors
3. **Production deployment**: Use production salt for live environment

## Contact PayU Support

If you can't find your merchant salt:
1. Contact PayU technical support
2. Provide your merchant ID: `AbM7T2`
3. Request your merchant salt for hash generation

## File Locations to Update

1. **Primary**: `lib/services/payment/payu_service.dart` (line 407)
2. **Optional**: Create `lib/config/payu_config.dart` for better organization
3. **Security**: Add salt to `.gitignore` if storing in separate file

---

**🔥 URGENT**: Replace `YOUR_MERCHANT_SALT_HERE` with your actual PayU merchant salt to fix the "Invalid Hash" error.
