/**
 * PayU Response Debug Script
 * This script helps diagnose PayU response handling issues
 */

const mysql = require('mysql2/promise');

// Database configuration - update with your actual settings
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'ecoplug',
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
};

async function debugPayUResponse() {
    console.log('🔍 PayU Response Debug Script');
    console.log('============================\n');

    let connection;
    try {
        // Test database connection
        console.log('1. Testing database connection...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Database connection successful\n');

        // Check payment_history table structure
        console.log('2. Checking payment_history table structure...');
        const [columns] = await connection.execute(`
            SHOW COLUMNS FROM payment_history
        `);
        
        console.log('📋 Table columns:');
        columns.forEach(col => {
            console.log(`   - ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
        });
        console.log('');

        // Check for recent transactions
        console.log('3. Checking recent transactions...');
        const [recentTxns] = await connection.execute(`
            SELECT id, txnid, status, amount, payment_method, created_at, updated_at
            FROM payment_history 
            ORDER BY created_at DESC 
            LIMIT 5
        `);

        if (recentTxns.length > 0) {
            console.log('📊 Recent transactions:');
            recentTxns.forEach(txn => {
                console.log(`   - ID: ${txn.id}, TxnID: ${txn.txnid}, Status: ${txn.status}, Amount: ${txn.amount}`);
            });
        } else {
            console.log('⚠️ No transactions found in payment_history table');
        }
        console.log('');

        // Check for pending transactions
        console.log('4. Checking pending transactions...');
        const [pendingTxns] = await connection.execute(`
            SELECT id, txnid, status, amount, created_at
            FROM payment_history 
            WHERE status = 'PENDING'
            ORDER BY created_at DESC
        `);

        if (pendingTxns.length > 0) {
            console.log('⏳ Pending transactions:');
            pendingTxns.forEach(txn => {
                console.log(`   - ID: ${txn.id}, TxnID: ${txn.txnid}, Amount: ${txn.amount}, Created: ${txn.created_at}`);
            });
        } else {
            console.log('✅ No pending transactions found');
        }
        console.log('');

        // Test transaction update
        console.log('5. Testing transaction update functionality...');
        
        if (pendingTxns.length > 0) {
            const testTxn = pendingTxns[0];
            console.log(`🧪 Testing update on transaction ID: ${testTxn.id}`);
            
            // Simulate PayU response update
            const [updateResult] = await connection.execute(`
                UPDATE payment_history
                SET payu_response = ?,
                    payu_hash = ?,
                    payu_status = ?,
                    updated_at = NOW()
                WHERE id = ?
            `, [
                JSON.stringify({ test: 'debug_update', timestamp: new Date() }),
                'debug_hash_123',
                'debug_status',
                testTxn.id
            ]);

            console.log(`📝 Update result - affected rows: ${updateResult.affectedRows}`);
            
            if (updateResult.affectedRows > 0) {
                console.log('✅ Transaction update test successful');
            } else {
                console.log('❌ Transaction update test failed');
            }
        } else {
            console.log('⚠️ No pending transactions to test update on');
        }

    } catch (error) {
        console.error('❌ Debug script error:', error);
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            errno: error.errno
        });
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔗 Database connection closed');
        }
    }
}

// Function to create a test transaction
async function createTestTransaction() {
    console.log('\n🧪 Creating test transaction...');
    
    let connection;
    try {
        connection = await mysql.createConnection(dbConfig);
        
        const testTxnId = `test_payu_${Date.now()}`;
        const [insertResult] = await connection.execute(`
            INSERT INTO payment_history 
            (user_id, amount, status, payment_method, txnid, transaction_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
            1, // user_id - update with a valid user ID
            100.00, // amount
            'PENDING', // status
            'payu', // payment_method
            testTxnId, // txnid
            testTxnId // transaction_id
        ]);

        console.log(`✅ Test transaction created with ID: ${insertResult.insertId}`);
        console.log(`📋 Transaction details: TxnID=${testTxnId}, Amount=100.00, Status=PENDING`);
        
        return { id: insertResult.insertId, txnid: testTxnId };
        
    } catch (error) {
        console.error('❌ Failed to create test transaction:', error);
        return null;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Main execution
async function main() {
    await debugPayUResponse();
    
    // Optionally create a test transaction
    const createTest = process.argv.includes('--create-test');
    if (createTest) {
        await createTestTransaction();
    }
    
    console.log('\n🏁 Debug script completed');
    console.log('\n💡 Tips:');
    console.log('   - Make sure your database credentials are correct');
    console.log('   - Check that the payment_history table exists');
    console.log('   - Verify that your server is using the correct database');
    console.log('   - Run with --create-test to create a test transaction');
}

if (require.main === module) {
    main().catch(error => {
        console.error('💥 Debug script failed:', error);
        process.exit(1);
    });
}

module.exports = { debugPayUResponse, createTestTransaction };
