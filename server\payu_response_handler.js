const express = require('express');
const mysql = require('mysql2/promise');
const crypto = require('crypto');
const router = express.Router();

// Database configuration - update with your actual database settings
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'ecoplug',
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    multipleStatements: false
};

// Create connection pool for better performance with error handling
let pool;
try {
    pool = mysql.createPool(dbConfig);
    console.log('✅ PAYU: Database pool created successfully');

    // Test the connection
    pool.getConnection()
        .then(connection => {
            console.log('✅ PAYU: Database connection test successful');
            connection.release();
        })
        .catch(error => {
            console.error('❌ PAYU: Database connection test failed:', error.message);
        });
} catch (error) {
    console.error('❌ PAYU: Failed to create database pool:', error);
}

/**
 * Enhanced PayU Response Handler Endpoint
 * Handles PayU payment gateway responses with comprehensive error handling,
 * transaction verification, and database updates
 */
router.post('/user/payment/response-payu', async (req, res) => {
    const startTime = Date.now();
    const requestId = `payu_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log(`🔔 PAYU [${requestId}]: ========== ENHANCED PAYU RESPONSE HANDLER START ==========`);
    console.log(`🔔 PAYU [${requestId}]: Timestamp: ${new Date().toISOString()}`);
    console.log(`🔔 PAYU [${requestId}]: Request body:`, JSON.stringify(req.body, null, 2));

    try {
        // 1. VALIDATE AND EXTRACT REQUEST DATA
        const validationResult = validatePayURequest(req.body, requestId);
        if (!validationResult.isValid) {
            console.error(`❌ PAYU [${requestId}]: Validation failed: ${validationResult.error}`);
            // FIXED: Return simple string response for PayU
            return res.status(400).send('failure');
        }

        const { status, txnid, hash, response } = validationResult.data;
        console.log(`✅ PAYU [${requestId}]: Request validation passed`);

        // 2. FIND TRANSACTION IN DATABASE
        const transaction = await findTransactionByTxnId(txnid, requestId);
        if (!transaction) {
            console.error(`❌ PAYU [${requestId}]: Transaction not found: ${txnid}`);
            // FIXED: Return simple string response for PayU
            return res.status(200).send('failure');
        }

        console.log(`✅ PAYU [${requestId}]: Transaction found:`, {
            id: transaction.id,
            current_status: transaction.status,
            amount: transaction.amount,
            user_id: transaction.user_id
        });

        // 3. VERIFY HASH (if provided and merchant key is available)
        if (hash && process.env.PAYU_MERCHANT_KEY && process.env.PAYU_SALT) {
            const isHashValid = verifyPayUHash(response, hash, requestId);
            if (!isHashValid) {
                console.error(`❌ PAYU [${requestId}]: Hash verification failed`);
                // FIXED: Return simple string response for PayU
                return res.status(200).send('failure');
            }
            console.log(`✅ PAYU [${requestId}]: Hash verification passed`);
        }

        // 4. MAP STATUS AND UPDATE TRANSACTION
        const mappedStatus = mapPayUStatusToDatabase(status, requestId);

        console.log(`🔄 PAYU [${requestId}]: CRITICAL - About to update transaction`);
        console.log(`🔄 PAYU [${requestId}]: Transaction ID: ${transaction.id}`);
        console.log(`🔄 PAYU [${requestId}]: Current Status: ${transaction.status}`);
        console.log(`🔄 PAYU [${requestId}]: PayU Status: ${status}`);
        console.log(`🔄 PAYU [${requestId}]: Mapped Status: ${mappedStatus}`);
        console.log(`🔄 PAYU [${requestId}]: Transaction Amount: ${transaction.amount}`);

        const updateResult = await updateTransactionStatus(
            transaction.id,
            mappedStatus,
            {
                payu_response: JSON.stringify(response || {}),
                payu_hash: hash || '',
                payu_status: status,
                request_id: requestId,
                processed_at: new Date()
            },
            requestId
        );

        if (!updateResult.success) {
            console.error(`❌ PAYU [${requestId}]: Database update failed: ${updateResult.error}`);
            // FIXED: Return simple string response for PayU
            return res.status(200).send('failure');
        }

        console.log(`✅ PAYU [${requestId}]: Transaction updated: ${transaction.status} → ${mappedStatus}`);

        // 5. UPDATE WALLET BALANCE (if payment successful)
        if (mappedStatus === 'COMPLETED') {
            const walletUpdateResult = await updateWalletBalance(
                transaction.user_id, 
                transaction.amount, 
                requestId
            );
            
            if (walletUpdateResult.success) {
                console.log(`✅ PAYU [${requestId}]: Wallet balance updated for user ${transaction.user_id}`);
            } else {
                console.error(`❌ PAYU [${requestId}]: Wallet update failed: ${walletUpdateResult.error}`);
            }
        }

        // 6. SEND SUCCESS RESPONSE - FIXED according to PayU documentation
        const processingTime = Date.now() - startTime;
        console.log(`✅ PAYU [${requestId}]: Processing completed in ${processingTime}ms`);
        console.log(`🔔 PAYU [${requestId}]: ========== ENHANCED PAYU RESPONSE HANDLER END ==========`);

        // CRITICAL FIX: Return simple string response as required by PayU
        // PayU expects: "success" for successful processing, "failure" for errors
        if (mappedStatus === 'COMPLETED') {
            res.status(200).send('success');
        } else if (mappedStatus === 'REJECTED') {
            res.status(200).send('failure');
        } else {
            res.status(200).send('pending');
        }

    } catch (error) {
        const processingTime = Date.now() - startTime;
        console.error(`❌ PAYU [${requestId}]: Processing failed after ${processingTime}ms`);
        console.error(`❌ PAYU [${requestId}]: Error:`, error);
        console.error(`❌ PAYU [${requestId}]: Stack trace:`, error.stack);

        // FIXED: Return simple string response for PayU
        res.status(200).send('failure');
    }
});

/**
 * Validate PayU request data
 */
function validatePayURequest(body, requestId) {
    console.log(`🔍 PAYU [${requestId}]: Validating request data...`);
    
    if (!body || typeof body !== 'object') {
        return { isValid: false, error: 'Invalid request body' };
    }

    const { status, txnid, hash, response } = body;

    // Check required fields
    if (!status || typeof status !== 'string' || status.trim() === '') {
        return { isValid: false, error: 'Missing or invalid status field' };
    }

    if (!txnid || typeof txnid !== 'string' || txnid.trim() === '') {
        return { isValid: false, error: 'Missing or invalid txnid field' };
    }

    // Validate transaction ID format
    if (txnid.length < 5 || txnid.length > 100) {
        return { isValid: false, error: 'Invalid transaction ID format' };
    }

    // ADDED: Validate PayU response data structure
    if (!response || typeof response !== 'object') {
        return { isValid: false, error: 'Missing or invalid PayU response data' };
    }

    // ADDED: Validate essential PayU response fields
    const requiredPayUFields = ['txnid', 'amount', 'productinfo', 'firstname', 'email'];
    for (const field of requiredPayUFields) {
        if (!response[field]) {
            console.log(`⚠️ PAYU [${requestId}]: Missing PayU response field: ${field}`);
            // Log warning but don't fail validation (some fields might be optional)
        }
    }

    console.log(`✅ PAYU [${requestId}]: PayU request validation passed`);
    return {
        isValid: true,
        data: {
            status: status.trim(),
            txnid: txnid.trim(),
            hash: hash || '',
            response: response || {}
        }
    };
}

/**
 * Find transaction by transaction ID with multiple fallback strategies
 */
async function findTransactionByTxnId(txnid, requestId) {
    console.log(`🔍 PAYU [${requestId}]: Searching for transaction: ${txnid}`);
    
    const connection = await pool.getConnection();
    
    try {
        // Try multiple possible transaction ID fields and formats
        const queries = [
            // Direct txnid match
            'SELECT * FROM payment_history WHERE txnid = ? LIMIT 1',
            // Transaction ID field
            'SELECT * FROM payment_history WHERE transaction_id = ? LIMIT 1',
            // ID field (if txnid is actually the primary key)
            'SELECT * FROM payment_history WHERE id = ? LIMIT 1',
            // Remark field (sometimes txnid is stored in remark)
            'SELECT * FROM payment_history WHERE remark LIKE ? LIMIT 1',
            // Reference number field
            'SELECT * FROM payment_history WHERE reference_number = ? LIMIT 1'
        ];
        
        for (let i = 0; i < queries.length; i++) {
            const query = queries[i];
            const searchValue = query.includes('LIKE') ? `%${txnid}%` : txnid;
            
            console.log(`🔍 PAYU [${requestId}]: Trying query ${i + 1}: ${query}`);
            
            const [rows] = await connection.execute(query, [searchValue]);
            
            if (rows.length > 0) {
                console.log(`✅ PAYU [${requestId}]: Transaction found using query ${i + 1}`);
                return rows[0];
            }
        }
        
        console.log(`❌ PAYU [${requestId}]: Transaction not found with any query`);
        return null;
        
    } catch (error) {
        console.error(`❌ PAYU [${requestId}]: Database search error:`, error);
        throw error;
    } finally {
        connection.release();
    }
}

/**
 * Verify PayU hash for security - FIXED according to PayU official documentation
 */
function verifyPayUHash(responseData, receivedHash, requestId) {
    console.log(`🔐 PAYU [${requestId}]: Verifying PayU hash...`);

    try {
        const merchantKey = process.env.PAYU_MERCHANT_KEY;
        const salt = process.env.PAYU_SALT;

        if (!merchantKey || !salt) {
            console.log(`⚠️ PAYU [${requestId}]: Merchant key or salt not configured, skipping hash verification`);
            return true; // Skip verification if not configured
        }

        // CRITICAL FIX: Construct hash string according to PayU OFFICIAL documentation
        // RESPONSE Hash Format: SALT|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key
        const status = responseData.status || '';
        const email = responseData.email || '';
        const firstname = responseData.firstname || '';
        const productinfo = responseData.productinfo || '';
        const amount = responseData.amount || '';
        const txnid = responseData.txnid || '';
        const udf1 = responseData.udf1 || '';
        const udf2 = responseData.udf2 || '';
        const udf3 = responseData.udf3 || '';
        const udf4 = responseData.udf4 || '';
        const udf5 = responseData.udf5 || '';

        // OFFICIAL PayU Response Hash Format
        const hashString = `${salt}|${status}||||||${udf5}|${udf4}|${udf3}|${udf2}|${udf1}|${email}|${firstname}|${productinfo}|${amount}|${txnid}|${merchantKey}`;

        console.log(`🔐 PAYU [${requestId}]: OFFICIAL Response Hash string: ${hashString}`);

        const calculatedHash = crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();

        console.log(`🔐 PAYU [${requestId}]: Calculated hash: ${calculatedHash}`);
        console.log(`🔐 PAYU [${requestId}]: Received hash: ${receivedHash}`);

        const isValid = calculatedHash === receivedHash.toLowerCase();
        console.log(`${isValid ? '✅' : '❌'} PAYU [${requestId}]: Hash verification ${isValid ? 'passed' : 'failed'}`);

        return isValid;

    } catch (error) {
        console.error(`❌ PAYU [${requestId}]: Hash verification error:`, error);
        return false;
    }
}

/**
 * Map PayU status to database status with comprehensive mapping
 */
function mapPayUStatusToDatabase(payuStatus, requestId) {
    console.log(`🔄 PAYU [${requestId}]: Mapping status: ${payuStatus}`);

    const statusLower = payuStatus.toLowerCase().trim();

    const statusMapping = {
        // Success statuses
        'success': 'COMPLETED',
        'successful': 'COMPLETED',
        'completed': 'COMPLETED',
        'complete': 'COMPLETED',
        'captured': 'COMPLETED',

        // Failure statuses
        'failed': 'REJECTED',
        'failure': 'REJECTED',
        'cancelled': 'REJECTED',
        'canceled': 'REJECTED',
        'rejected': 'REJECTED',
        'error': 'REJECTED',
        'declined': 'REJECTED',
        'invalid': 'REJECTED',

        // Pending statuses
        'pending': 'PENDING',
        'processing': 'PENDING',
        'initiated': 'PENDING',
        'in_progress': 'PENDING',
        'submitted': 'PENDING',
        'authorized': 'PENDING',

        // Timeout/Unknown
        'timeout': 'REJECTED',
        'expired': 'REJECTED',
        'unknown': 'REJECTED'
    };

    const mappedStatus = statusMapping[statusLower];

    if (mappedStatus) {
        console.log(`✅ PAYU [${requestId}]: Status mapped: ${payuStatus} → ${mappedStatus}`);
        return mappedStatus;
    } else {
        console.log(`⚠️ PAYU [${requestId}]: Unknown status: ${payuStatus}, defaulting to REJECTED`);
        return 'REJECTED';
    }
}

/**
 * Update transaction status in database with comprehensive logging
 */
async function updateTransactionStatus(transactionId, newStatus, additionalData, requestId) {
    console.log(`💾 PAYU [${requestId}]: Updating transaction ${transactionId} to status: ${newStatus}`);

    if (!pool) {
        console.error(`❌ PAYU [${requestId}]: Database pool not available`);
        return { success: false, error: 'Database pool not available' };
    }

    let connection;
    try {
        connection = await pool.getConnection();
        console.log(`🔗 PAYU [${requestId}]: Database connection acquired`);

        // First, get current transaction details for logging
        const [currentRows] = await connection.execute(
            'SELECT * FROM payment_history WHERE id = ?',
            [transactionId]
        );

        if (currentRows.length === 0) {
            throw new Error(`Transaction ${transactionId} not found for update`);
        }

        const currentTransaction = currentRows[0];
        console.log(`📊 PAYU [${requestId}]: Current transaction status: ${currentTransaction.status}`);

        // Prevent duplicate updates
        if (currentTransaction.status === newStatus) {
            console.log(`⚠️ PAYU [${requestId}]: Transaction already has status ${newStatus}, skipping update`);
            return { success: true, message: 'Status already updated' };
        }

        // Update the transaction with enhanced data
        const updateQuery = `
            UPDATE payment_history
            SET status = ?,
                remark = ?,
                payu_response = ?,
                payu_hash = ?,
                payu_status = ?,
                updated_at = NOW(),
                processed_at = NOW()
            WHERE id = ?
        `;

        const updateValues = [
            newStatus,
            getRemarkForStatus(newStatus),
            additionalData.payu_response || '',
            additionalData.payu_hash || '',
            additionalData.payu_status || '',
            transactionId
        ];

        console.log(`🔄 PAYU [${requestId}]: Executing update query...`);
        const [updateResult] = await connection.execute(updateQuery, updateValues);
        console.log(`📝 PAYU [${requestId}]: Update result - affected rows: ${updateResult.affectedRows}`);

        if (updateResult.affectedRows === 0) {
            throw new Error('No rows were updated - transaction may not exist');
        }

        // Verify the update was successful
        const [updatedRows] = await connection.execute(
            'SELECT * FROM payment_history WHERE id = ?',
            [transactionId]
        );

        if (updatedRows.length > 0) {
            const updatedTransaction = updatedRows[0];
            console.log(`✅ PAYU [${requestId}]: Database updated successfully: ${currentTransaction.status} → ${updatedTransaction.status}`);
            return { success: true, transaction: updatedTransaction };
        } else {
            throw new Error('Failed to verify database update');
        }

    } catch (error) {
        console.error(`❌ PAYU [${requestId}]: Database update error:`, error);
        console.error(`❌ PAYU [${requestId}]: Error details:`, {
            message: error.message,
            code: error.code,
            errno: error.errno,
            sqlState: error.sqlState
        });
        return { success: false, error: error.message };
    } finally {
        if (connection) {
            connection.release();
            console.log(`🔗 PAYU [${requestId}]: Database connection released`);
        }
    }
}

/**
 * Update wallet balance for successful payments
 */
async function updateWalletBalance(userId, amount, requestId) {
    console.log(`💰 PAYU [${requestId}]: Updating wallet balance for user ${userId}, amount: ${amount}`);

    const connection = await pool.getConnection();

    try {
        // Start transaction for atomic wallet update
        await connection.beginTransaction();

        // Get current wallet balance
        const [walletRows] = await connection.execute(
            'SELECT balance FROM wallets WHERE user_id = ? FOR UPDATE',
            [userId]
        );

        let currentBalance = 0;
        if (walletRows.length > 0) {
            currentBalance = parseFloat(walletRows[0].balance) || 0;
        }

        const newBalance = currentBalance + parseFloat(amount);
        console.log(`💰 PAYU [${requestId}]: Wallet balance: ${currentBalance} → ${newBalance}`);

        // Update or insert wallet record
        if (walletRows.length > 0) {
            await connection.execute(
                'UPDATE wallets SET balance = ?, updated_at = NOW() WHERE user_id = ?',
                [newBalance, userId]
            );
        } else {
            await connection.execute(
                'INSERT INTO wallets (user_id, balance, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
                [userId, newBalance]
            );
        }

        await connection.commit();
        console.log(`✅ PAYU [${requestId}]: Wallet balance updated successfully`);

        return { success: true, oldBalance: currentBalance, newBalance: newBalance };

    } catch (error) {
        await connection.rollback();
        console.error(`❌ PAYU [${requestId}]: Wallet update error:`, error);
        return { success: false, error: error.message };
    } finally {
        connection.release();
    }
}

/**
 * Generate appropriate remark for transaction status
 */
function getRemarkForStatus(status) {
    const remarkMapping = {
        'COMPLETED': 'Payment Successful - Wallet Balance Added',
        'REJECTED': 'Payment Failed - Transaction Cancelled',
        'PENDING': 'Payment Processing - Please Wait'
    };

    return remarkMapping[status] || 'Payment Status Updated';
}

/**
 * Health check endpoint for PayU service
 */
router.get('/payu/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'Enhanced PayU Response Handler',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        database: 'connected'
    });
});

/**
 * Get PayU transaction statistics (for monitoring)
 */
router.get('/payu/stats', async (req, res) => {
    try {
        const connection = await pool.getConnection();

        const [stats] = await connection.execute(`
            SELECT
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as successful_transactions,
                COUNT(CASE WHEN status = 'REJECTED' THEN 1 END) as failed_transactions,
                COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_transactions,
                SUM(CASE WHEN status = 'COMPLETED' THEN amount ELSE 0 END) as total_successful_amount
            FROM payment_history
            WHERE payment_method = 'payu'
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `);

        connection.release();

        res.json({
            success: true,
            period: 'last_24_hours',
            statistics: stats[0],
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('PayU stats error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch statistics'
        });
    }
});

// Error handling middleware
router.use((error, req, res, next) => {
    console.error('PayU Router Error:', error);
    res.status(500).json({
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred'
    });
});

module.exports = router;

// Usage in your main server file (app.js or server.js):
// const payuHandler = require('./payu_response_handler');
// app.use('/api/v1', payuHandler);
