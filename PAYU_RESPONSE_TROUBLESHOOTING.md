# PayU Response Troubleshooting Guide

## Issue: Payment Status Not Being Updated

### Quick Diagnosis Steps

1. **Run the debug script:**
   ```bash
   node debug_payu_response.js
   ```

2. **Check server logs** when a PayU payment is made - look for:
   ```
   🔔 PAYU [requestId]: ========== ENHANCED PAYU RESPONSE HANDLER START ==========
   ```

3. **Test the endpoint directly:**
   ```bash
   node server/test_payu_response.js
   ```

### Common Issues and Solutions

#### 1. Database Connection Issues

**Symptoms:**
- Server logs show: `❌ PAYU: Database pool not available`
- No database updates happening

**Solution:**
Update database credentials in `server/payu_response_handler.js`:
```javascript
const dbConfig = {
    host: 'localhost',        // Your DB host
    user: 'your_db_user',     // Your DB username  
    password: 'your_db_pass', // Your DB password
    database: 'ecoplug',      // Your DB name
    // ... other config
};
```

#### 2. Missing Database Table/Columns

**Symptoms:**
- Error: `Table 'payment_history' doesn't exist`
- Error: `Unknown column 'payu_response'`

**Solution:**
Run the database schema update:
```sql
-- Check if table exists
SHOW TABLES LIKE 'payment_history';

-- Add missing columns if needed
ALTER TABLE payment_history 
ADD COLUMN IF NOT EXISTS payu_response LONGTEXT,
ADD COLUMN IF NOT EXISTS payu_hash VARCHAR(255),
ADD COLUMN IF NOT EXISTS payu_status VARCHAR(50),
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP NULL;
```

#### 3. Transaction Not Found

**Symptoms:**
- Server logs: `❌ PAYU: Transaction not found: txn_id`

**Solution:**
Check if transaction exists in database:
```sql
SELECT * FROM payment_history WHERE txnid = 'your_transaction_id';
```

If missing, ensure the transaction is created before PayU payment starts.

#### 4. Server Endpoint Not Responding

**Symptoms:**
- Client logs: `❌ PAYU API: Network Error: No response received`
- No server logs for PayU requests

**Solution:**
1. Verify server is running
2. Check if PayU handler is properly registered:
   ```javascript
   // In your main server file (app.js/server.js)
   const payuHandler = require('./server/payu_response_handler');
   app.use('/api/v1', payuHandler);
   ```

#### 5. Duplicate Response Handlers

**Symptoms:**
- Conflicting behavior
- Some responses work, others don't

**Solution:**
1. Remove duplicate files:
   - Keep: `server/payu_response_handler.js`
   - Remove: `server_payu_response_handler_fixed.js`

2. Ensure only one handler is registered in your server

### Testing Your Fix

#### Step 1: Database Test
```bash
node debug_payu_response.js --create-test
```

#### Step 2: Endpoint Test
```bash
node server/test_payu_response.js
```

#### Step 3: Client Test
1. Make a small PayU payment (₹1)
2. Check client logs for:
   ```
   🔔 PAYU: FIXED: Forwarding callback exactly as received from PayU
   ✅ PAYU: Backend response received
   ```

3. Check server logs for:
   ```
   ✅ PAYU [requestId]: Database updated successfully: PENDING → COMPLETED
   ```

### Expected Log Flow

**Client Side:**
```
🔔 PAYU: ========== FIXED PAYU RESPONSE HANDLER ==========
🔔 PAYU: Original Status: success
🚀 PAYU: Backend attempt 1/3
✅ PAYU: Backend response received
```

**Server Side:**
```
🔔 PAYU [requestId]: ========== ENHANCED PAYU RESPONSE HANDLER START ==========
✅ PAYU [requestId]: Request validation passed
✅ PAYU [requestId]: Transaction found: {...}
✅ PAYU [requestId]: Status mapped: success → COMPLETED
💾 PAYU [requestId]: Updating transaction X to status: COMPLETED
✅ PAYU [requestId]: Database updated successfully: PENDING → COMPLETED
```

### Still Not Working?

1. **Check your server's main file** (app.js/server.js) includes:
   ```javascript
   const payuHandler = require('./server/payu_response_handler');
   app.use('/api/v1', payuHandler);
   ```

2. **Verify API endpoint URL** in client matches server:
   - Client: `/user/payment/response-payu`
   - Server: `router.post('/user/payment/response-payu', ...)`

3. **Check database permissions** - ensure your DB user can UPDATE the payment_history table

4. **Enable detailed logging** by setting environment variable:
   ```bash
   DEBUG=payu:* node your_server.js
   ```

### Emergency Fallback

If nothing works, you can manually update a transaction for testing:
```sql
UPDATE payment_history 
SET status = 'COMPLETED', 
    remark = 'Manual Test Update',
    updated_at = NOW()
WHERE txnid = 'your_test_transaction_id';
```

This will help you verify if the issue is with the PayU handler or the database itself.
