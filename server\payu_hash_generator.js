/**
 * PayU Hash Generation Endpoint
 * This endpoint generates secure hashes for PayU payment integration
 * 
 * IMPORTANT: This file should be added to your backend server
 * Usage: Add this to your Express.js server routes
 */

const express = require('express');
const crypto = require('crypto');
const router = express.Router();

// PayU Configuration - Set these environment variables on your server
const PAYU_MERCHANT_KEY = process.env.PAYU_MERCHANT_KEY || 'YOUR_MERCHANT_KEY';
const PAYU_SALT = process.env.PAYU_SALT || 'YOUR_SALT_KEY';

/**
 * Generate PayU Hash Endpoint
 * POST /api/v1/user/payu/get-hash
 */
router.post('/user/payu/get-hash', async (req, res) => {
    const requestId = `hash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
        console.log(`🔐 PAYU HASH [${requestId}]: ========== HASH GENERATION REQUEST ==========`);
        console.log(`🔐 PAYU HASH [${requestId}]: Request body:`, JSON.stringify(req.body, null, 2));
        
        const { hashName, hashString, hashType, postSalt } = req.body;
        
        // Validate required parameters
        if (!hashName || !hashString) {
            console.error(`❌ PAYU HASH [${requestId}]: Missing required parameters`);
            return res.status(400).json({
                success: false,
                error: 'MISSING_PARAMETERS',
                message: 'hashName and hashString are required',
                request_id: requestId
            });
        }
        
        console.log(`🔐 PAYU HASH [${requestId}]: HashName: ${hashName}`);
        console.log(`🔐 PAYU HASH [${requestId}]: HashString length: ${hashString.length}`);
        console.log(`🔐 PAYU HASH [${requestId}]: HashType: ${hashType || 'not specified'}`);
        
        // Generate hash based on PayU requirements
        let generatedHash = '';
        
        if (hashName === 'payment_hash' || hashName.includes('checkout')) {
            // For payment hashes, use the standard PayU formula
            // Format: key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||salt
            const hashData = `${hashString}${PAYU_SALT}`;
            generatedHash = crypto.createHash('sha512').update(hashData).digest('hex');
            console.log(`🔐 PAYU HASH [${requestId}]: Generated payment hash using SHA512`);
            
        } else if (hashName === 'get_checkout_details') {
            // For checkout details hash
            const hashData = `${hashString}${PAYU_SALT}`;
            generatedHash = crypto.createHash('sha512').update(hashData).digest('hex');
            console.log(`🔐 PAYU HASH [${requestId}]: Generated checkout details hash`);
            
        } else if (hashName === 'get_sdk_configuration') {
            // For SDK configuration hash
            const hashData = `${hashString}${PAYU_SALT}`;
            generatedHash = crypto.createHash('sha512').update(hashData).digest('hex');
            console.log(`🔐 PAYU HASH [${requestId}]: Generated SDK configuration hash`);
            
        } else if (hashName === 'quickPayEvent') {
            // For quickPay events hash
            const hashData = `${hashString}${PAYU_SALT}`;
            generatedHash = crypto.createHash('sha512').update(hashData).digest('hex');
            console.log(`🔐 PAYU HASH [${requestId}]: Generated quickPay event hash`);
            
        } else if (hashName === 'get_all_offer_details') {
            // For offer details hash
            const hashData = `${hashString}${PAYU_SALT}`;
            generatedHash = crypto.createHash('sha512').update(hashData).digest('hex');
            console.log(`🔐 PAYU HASH [${requestId}]: Generated offer details hash`);
            
        } else {
            // For unknown hash types, generate a generic hash
            const hashData = `${hashString}${PAYU_SALT}`;
            generatedHash = crypto.createHash('sha512').update(hashData).digest('hex');
            console.log(`🔐 PAYU HASH [${requestId}]: Generated generic hash for type: ${hashName}`);
        }
        
        console.log(`🔐 PAYU HASH [${requestId}]: Hash generated successfully`);
        console.log(`🔐 PAYU HASH [${requestId}]: Hash length: ${generatedHash.length}`);
        console.log(`🔐 PAYU HASH [${requestId}]: ========== HASH GENERATION SUCCESS ==========`);
        
        // Return the generated hash
        res.json({
            success: true,
            hash: generatedHash,
            hashName: hashName,
            request_id: requestId,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error(`❌ PAYU HASH [${requestId}]: Hash generation error:`, error);
        console.log(`🔐 PAYU HASH [${requestId}]: ========== HASH GENERATION ERROR ==========`);
        
        res.status(500).json({
            success: false,
            error: 'HASH_GENERATION_FAILED',
            message: 'Failed to generate PayU hash',
            request_id: requestId
        });
    }
});

/**
 * Health check endpoint for PayU hash service
 */
router.get('/payu/hash/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'PayU Hash Generator',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        merchant_key_configured: !!PAYU_MERCHANT_KEY && PAYU_MERCHANT_KEY !== 'YOUR_MERCHANT_KEY',
        salt_configured: !!PAYU_SALT && PAYU_SALT !== 'YOUR_SALT_KEY'
    });
});

module.exports = router;

/**
 * INSTALLATION INSTRUCTIONS:
 * 
 * 1. Add this file to your server project
 * 2. In your main server file (app.js or server.js), add:
 *    const payuHashRouter = require('./payu_hash_generator');
 *    app.use('/api/v1', payuHashRouter);
 * 
 * 3. Set environment variables:
 *    export PAYU_MERCHANT_KEY="your_actual_merchant_key"
 *    export PAYU_SALT="your_actual_salt_key"
 * 
 * 4. Test the endpoint:
 *    curl -X POST http://localhost:3000/api/v1/user/payu/get-hash \
 *         -H "Content-Type: application/json" \
 *         -H "Authorization: Bearer YOUR_TOKEN" \
 *         -d '{"hashName":"payment_hash","hashString":"test_string"}'
 */
