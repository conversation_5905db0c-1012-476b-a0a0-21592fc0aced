# PayU Hash Generation Solution

## Problem
PayU payments are failing with "Invalid Hash" error (error code 5019) because the backend endpoint `/user/payu/get-hash` doesn't exist.

## Root Cause
According to PayU's official documentation, hash generation must be done server-side using the merchant's secret salt. Client-side hash generation without the proper salt will always fail PayU's validation.

## Immediate Solution Required

### 1. Implement Backend Hash Generation Endpoint

You need to add the following endpoint to your backend server:

**Endpoint:** `POST /api/v1/user/payu/get-hash`

**Request Body:**
```json
{
  "hashName": "get_checkout_details",
  "hashString": "AbM7T2|get_checkout_details|{...}|",
  "hashType": "",
  "postSalt": ""
}
```

**Response:**
```json
{
  "success": true,
  "hash": "generated_sha512_hash_here",
  "hashName": "get_checkout_details"
}
```

### 2. Backend Implementation (Node.js/Express)

```javascript
const crypto = require('crypto');

// PayU Configuration - Set these in your environment variables
const PAYU_MERCHANT_KEY = process.env.PAYU_MERCHANT_KEY || 'AbM7T2';
const PAYU_SALT = process.env.PAYU_SALT || 'YOUR_ACTUAL_SALT';

app.post('/api/v1/user/payu/get-hash', (req, res) => {
    try {
        const { hashName, hashString, hashType, postSalt } = req.body;
        
        if (!hashName || !hashString) {
            return res.status(400).json({
                success: false,
                error: 'hashName and hashString are required'
            });
        }
        
        let generatedHash = '';
        
        if (hashType === "V2") {
            // HMAC SHA256 for V2 hashes
            generatedHash = crypto
                .createHmac('sha256', PAYU_SALT)
                .update(hashString)
                .digest('hex');
        } else if (hashName === "mcpLookup") {
            // HMAC SHA1 for MCP lookup
            generatedHash = crypto
                .createHmac('sha1', PAYU_SALT)
                .update(hashString)
                .digest('hex');
        } else if (postSalt) {
            // SHA512 with post salt
            const finalString = hashString + PAYU_SALT + postSalt;
            generatedHash = crypto
                .createHash('sha512')
                .update(finalString)
                .digest('hex');
        } else {
            // Standard SHA512 hash
            const finalString = hashString + PAYU_SALT;
            generatedHash = crypto
                .createHash('sha512')
                .update(finalString)
                .digest('hex');
        }
        
        res.json({
            success: true,
            hash: generatedHash,
            hashName: hashName
        });
        
    } catch (error) {
        console.error('Hash generation error:', error);
        res.status(500).json({
            success: false,
            error: 'Hash generation failed'
        });
    }
});
```

### 3. Get Your PayU Merchant Salt

1. Log in to your PayU Dashboard
2. Go to Settings > API Keys
3. Copy your Merchant Key and Salt
4. Set them as environment variables:
   ```bash
   export PAYU_MERCHANT_KEY="your_merchant_key"
   export PAYU_SALT="your_merchant_salt"
   ```

### 4. Test the Endpoint

```bash
curl -X POST http://localhost:3000/api/v1/user/payu/get-hash \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "hashName": "payment_hash",
       "hashString": "AbM7T2|txn123|100|product|John|<EMAIL>|||||||||",
       "hashType": "",
       "postSalt": ""
     }'
```

## Current Status

✅ **App Fixes Applied:**
- Fixed ticker provider error in AddBalanceSheet
- Fixed PayU payment completer race condition
- Improved error handling and logging
- Added proper fallback mechanisms

❌ **Still Needs Backend Work:**
- Implement `/user/payu/get-hash` endpoint
- Configure PayU merchant salt on server
- Test hash generation with actual PayU credentials

## Temporary Workaround

The app currently has a fallback mechanism that handles hash generation failures gracefully. However, for production use, you MUST implement the backend hash generation endpoint.

## Next Steps

1. **Immediate (Required for Production):**
   - Implement the backend hash generation endpoint
   - Configure PayU merchant salt on your server
   - Test the complete payment flow

2. **Optional Improvements:**
   - Add hash generation caching
   - Implement hash generation monitoring
   - Add detailed error logging

## Security Note

⚠️ **NEVER** expose your PayU merchant salt in client-side code. Hash generation must always be done server-side for security reasons.
