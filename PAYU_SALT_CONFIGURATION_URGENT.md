# 🚨 URGENT: PayU Salt Configuration Required

## Current Issue
**PayU payments are failing** with the error: `Hash cannot be null or empty`

## Root Cause
The PayU merchant salt is not configured in `lib/config/payu_config.dart`

## Quick Fix (5 minutes)

### Step 1: Get Your PayU Merchant Salt
1. **Log in to PayU Merchant Dashboard**: https://secure.payu.in/merchant/
2. **Navigate to**: Settings → API Keys (or Integration)
3. **Find your credentials**:
   - Merchant Key: `AbM7T2` ✅ (already configured)
   - **Merchant Salt**: `[COPY THIS VALUE]` ❌ (missing)

### Step 2: Update Configuration
Open `lib/config/payu_config.dart` and replace:

**BEFORE:**
```dart
static const String _testSalt = 'YOUR_TEST_SALT_HERE';
static const String _productionSalt = 'YOUR_PRODUCTION_SALT_HERE';
```

**AFTER:**
```dart
static const String _testSalt = 'your_actual_salt_here';        // Replace with your salt
static const String _productionSalt = 'your_actual_salt_here';  // Same or different salt
```

### Step 3: Test
1. Save the file
2. Restart the app: `flutter run`
3. Try a PayU payment
4. Check logs for: `✅ Hash generated successfully with merchant salt`

## Example Salt Values
- Salt looks like: `eCwWELxi` or `4R38IvwiV2` (8-32 characters)
- **DO NOT** use these examples - get your actual salt from PayU dashboard

## Security Note
- **Never commit the real salt to Git**
- Consider using environment variables for production
- Keep test and production salts separate

## Verification
After configuration, you should see in logs:
```
✅ PAYU: PayU configured for TEST environment
🔐 PAYU: ✅ Hash generated successfully with merchant salt
🔐 PAYU: Hash length: 128
```

Instead of:
```
❌ PAYU: PayU not configured: Update merchant salt in PayUConfig
🔐 PAYU: Hash length: 0
```

## Need Help?
If you can't find your PayU salt:
1. Contact PayU technical support
2. Provide your merchant ID: `AbM7T2`
3. Request your merchant salt for hash generation

---
**This fix is required for ALL PayU payments to work.**
